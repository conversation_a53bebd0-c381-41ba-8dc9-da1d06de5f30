<script lang="ts">
    import TwoStepForm from '../../lib/ui/TwoStepForm.svelte';
    import { Button, H1, H2, H3, P1, P2 } from '$lib/ui';

    let { data } = $props();
</script>

<svelte:head>
	<title>DSAT16 - Special Offer!</title>
</svelte:head>

<div class="webpage">
    <!-- Header Section -->
    <div class="header-section">
        <H1>DSAT16 Offer Đặc Biệt!</H1>
        <div class="limited-time">
            <H2>Dành riêng cho người tham gia webinar</H2>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-and-table">
            <!-- Left Side - Order Form -->
            <div id="order-form" class="order-form">
                <TwoStepForm {data} />
            </div>

            <!-- Right Side - Video -->
            <div class="video">

            </div>
        </div>
        
        <div class="product-info">
            {@render productTable()}
            <hr />
            {@render valueStack()}
        </div>
    </div>
</div>

{#snippet productTable()}
    <div class="product-table">
        <H3 --text-align="center">Tất cả mọi thứ trong gói</H3>
        <div class="table-container">
            <div class="table-row">
                <P1>DSAT16 (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 4.992.000đ</P1>
            </div>
            <div class="table-row">
                <P1>DSAT16 Bootcamp (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 1.160.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Meeting với Tutor 1600</P1>
                <P1 --text-color="var(--rose)">Trị giá 5.616.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Bonus: 67 đề thi SAT official</P1>
                <P1 --text-color="var(--rose)">Vô giá!</P1>
            </div>
            <div class="table-row">
                <P1>14 ngày hoàn tiền 100%</P1>
                <P1 --text-color="var(--rose)">Không rủi ro!</P1>
            </div>
        </div>
        <H3 --text-align="center">Tổng giá trị: <s style:color=var(--rose)>12.768.000đ</s></H3>
        <H3 --text-align="center">Mua trong hôm nay: <span style:color=var(--purple)>5.160.000đ</span></H3>
    </div>
{/snippet}

{#snippet valueStack()}
    <div class="value-stack">
        <H2 --text-align="center">Tất cả mọi thứ bạn sẽ được truy cập ngay lập tức nếu mua luôn trong hôm nay</H2>

        <div class="value-items">
            <div class="value-item">
                <H3>DSAT16 - Nền tảng tự học SAT (1 năm)</H3>
                <P2>
                    <ul>
                        <li>Question Bank gồm 5000+ câu hỏi với giải thích chi tiết</li>
                        <li>Tool học vocab giúp học từ vựng một cách tối ưu và hiệu quả</li>
                        <li>Hệ thống Gamify để giúp bạn luôn có động lực và không burn out</li>
                    </ul>
                </P2>
                <P1 isBold --text-color="var(--purple)">Trị giá 4.992.000đ</P1>
            </div>

            <div class="value-item">
                <H3>DSAT16 Bootcamp (1 năm)</H3>
                <P2>Khóa học bao gồm:</P2>
                <P2>
                    <ul>
                        <li>📽️ 11 video phân tích từng dạng câu hỏi</li>
                        <li>📚 Ghi chú bài giảng hoàn toàn bằng tiếng Anh giúp bạn rèn tư duy tiếng Anh</li>
                        <li>🔑 Chữa bài tập chi tiết</li>
                        <li>🗂️ Các tài liệu học thêm giúp bạn cải thiện khả năng đọc hiểu văn phong học thuật chuyên sâu của Digital SAT</li>
                    </ul>
                </P2>
                <P1 isBold --text-color="var(--purple)">Trị giá 1.160.000đ</P1>
            </div>

            <div class="value-item">
                <H3>Meeting với Tutor 1600</H3>
                <P1>Update tiến độ học tập, đảm bảo học sinh vẫn theo đúng lộ trình</P1>
                <P2>Phân tích chi tiết điểm mạnh, điểm yếu và lộ trình học tập cá nhân</P2>
                <P2>Giải đáp thắc mắc và chia sẻ kinh nghiệm thi thực tế</P2>
                <P1 isBold --text-color="var(--purple)">Trị giá 5.616.000đ</P1>
            </div>

            <div class="value-item">
                <H3>Bonus: 67 đề thi SAT official + Đảm bảo hoàn tiền</H3>
                <P1>Bộ sưu tập đầy đủ các đề thi SAT chính thức từ College Board</P1>
                <P1 isBold --text-color="var(--purple)">Vô giá!</P1>
            </div>
        </div>

        <div class="total-value">
            <H3>Tổng giá trị: <s style:color=var(--rose)>12.768.000đ</s></H3>
            <H2>Mua ngay hôm nay chỉ: <span style:color=var(--purple)>5.160.000đ</span></H2>
            <a href="#order-form">
                <Button fullWidth>Đăng ký ngay - Tiết kiệm 7.608.000đ!</Button>
            </a>
        </div>
    </div>
{/snippet}

<style>
    ul {
        padding-left: 2rem;
    }

    .webpage {
        width: 100%;
        font-size: 1rem;
        -webkit-tap-highlight-color: transparent;
        background: var(--white);
        min-height: 100vh;
    }

    .header-section {
        text-align: center;
        padding: 2rem;
        background: var(--sky-blue);
        border-bottom: 0.25rem solid var(--pitch-black);
    }

    .limited-time {
        margin-top: 1rem;
        padding: 1rem;
        background: var(--yellow);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        display: inline-block;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        max-width: 90rem;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-and-table {
        flex: 1;
        display: flex;
        gap: 2rem;
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .order-form {
        background: var(--light-aquamarine);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        height: fit-content;
        flex: 1 1 0;
    }

    .video {
        flex: 2 1 0;
    }

    .product-table {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        height: fit-content;
    }

    .table-container {
        margin: 1rem 0;
        border: 3px solid var(--pitch-black);
    }

    .table-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem;
        border-bottom: 0.125rem solid var(--pitch-black);
        background: var(--very-light-sky-blue);
    }

    .table-row:last-child {
        border-bottom: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .value-stack {
        background: var(--light-purple);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        max-width: 60rem;
        margin: 0 auto;
    }

    .value-items {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .value-item {
        background: var(--white);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .total-value {
        text-align: center;
        background: var(--yellow);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }
</style>